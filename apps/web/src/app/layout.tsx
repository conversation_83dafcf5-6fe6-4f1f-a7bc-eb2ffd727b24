import "@web/styles/globals.css";
import { type Metadata } from "next";
import { GoogleTagManager } from "@next/third-parties/google";
import { env } from "@web/env";
import Provider from "@web/_lib/context/theme-provider";
import Header from "@web/_lib/components/features/header";
import Footer from "@web/_lib/components/features/footer";
import { WebVitals } from "@web/_lib/components/analytics/web-vitals";

export const metadata: Metadata = {
  title: "<PERSON> (otechq) | Software Engineer",
  description:
    "Hi, I&apos;m <PERSON> — a software engineer who builds custom websites, mobile apps, and scalable digital solutions. Let&apos;s bring your ideas to life!",
  keywords: [
    "Software engineer",
    "Full-stack developer",
    "React",
    "React Native",
    "Next.js",
    "Node.js",
    "TypeScript",
    "JavaScript",
    "Mobile App Developer",
    "Web Developer",
    "Web App Developer",
    "Mobile App Developer",
    "Web Design",
    "Mobile App Design",
    "Web Development",
    "Mobile App Development",
    "Web Design Agency",
    "Mobile App Design Agency",
    "Web Development Agency",
    "Mobile App Development Agency",
    "Web Design Services",
    "Mobile App Design Services",
    "Web Development Services",
    "Mobile App Development Services",
  ],
  authors: [
    {
      name: "Ali Muksin",
      url: `${env.NEXT_PUBLIC_BASE_URL}`,
    },
  ],
  creator: "Ali Muksin",
  metadataBase: new URL(env.NEXT_PUBLIC_BASE_URL),
  openGraph: {
    title: "Ali Muksin (otechq) | Software Engineer",
    description:
      "Hi, I&apos;m Ali — a software engineer who builds custom websites, mobile apps, and scalable digital solutions. Let&apos;s bring your ideas to life!",
    url: `${env.NEXT_PUBLIC_BASE_URL}`,
    siteName: "Ali Muksin (otechq) | Software Engineer",
    images: [
      {
        url: `${env.NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL}/v1751012615/images/og-image_rfkswl.png`,
        alt: "Ali Muksin (otechq) | Software Engineer",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Ali Muksin (otechq) | Software Engineer",
    description:
      "Hi, I&apos;m Ali — a software engineer who builds custom websites, mobile apps, and scalable digital solutions. Let&apos;s bring your ideas to life!",
    images: [
      {
        url: `${env.NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL}/v1751012615/images/og-image_rfkswl.png`,
        alt: "Ali Muksin (otechq) | Software Engineer",
      },
    ],
    creator: "@a_lee0510",
  },
  icons: {
    icon: [
      {
        url: "/favicon.ico", // default icon
        rel: "icon",
      },
      {
        url: "/icon.svg", // general icon
        type: "image/svg+xml",
        sizes: "any",
      },
      {
        url: "/icon.png", // desktop icon
        type: "image/png",
        sizes: "96x96",
      },
    ],
    apple: [
      {
        url: "/apple-icon.png",
        sizes: "180x180",
        type: "image/png",
      },
    ],
  },
  manifest: "/manifest.json",
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>): React.ReactElement {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//res.cloudinary.com" />
        {/* Preload LCP image */}
        <link
          rel="preload"
          as="image"
          href={`${env.NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL}/v1751012617/images/persona_keuhv1.webp`}
          fetchPriority="high"
        />
      </head>
      <body className="bg-light-background dark:bg-dark-background h-full w-full overflow-hidden">
        <WebVitals />
        <Provider>
          <Header />
          {children}
          <Footer />
        </Provider>
      </body>
      <GoogleTagManager gtmId={env.GTM_ID} />
    </html>
  );
}
