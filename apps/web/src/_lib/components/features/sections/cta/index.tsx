import Image from "next/image";
import { WhatsappURL } from "@web/_lib/constants";
import { But<PERSON> } from "@web/_lib/components/ui";
import { env } from "@web/env";

export default function CTASection(): React.ReactElement {
  return (
    <section id="cta" className="text-dark-text px-4">
      <div className="bg-dark-card relative flex flex-col items-center gap-6 rounded-xl p-6 md:flex-row md:p-10">
        <div className="flex h-50 flex-1 flex-col items-start justify-center gap-2">
          <h1 className="text-2xl font-bold capitalize md:text-3xl">
            Got an idea but not sure where to start?
          </h1>
          <p className="text-md text-pretty md:text-lg">
            Let&apos;s figure it out together. I&apos;ll help turn your ideas
            into real working software that makes an impact — and grows with
            your business.
          </p>
          <Button
            href={WhatsappURL}
            external={true}
            variant="destructive"
            className="mt-4"
          >
            Let&apos;s Chat
          </Button>
        </div>
        <div id="3d-chat-bubble" className="relative size-50">
          <Image
            alt="3D cartoon woman illustration for contact section"
            src={`${env.NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL}/v1751012602/images/cartoon-woman_l3xr3b.png`}
            fill
            sizes="200px"
            className="object-cover"
            priority={false}
            quality={80}
            loading="lazy"
          />
        </div>
      </div>
    </section>
  );
}
